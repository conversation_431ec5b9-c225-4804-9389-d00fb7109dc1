<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="8a4b40a1-f069-4ffe-83a0-38521e22717f" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="2zjDlVO4fIthPIXUSaMFSLuC5M2" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Application.SparkImageRepoApplication.executor": "Run",
    "Maven.spark-image-repo [clean].executor": "Run",
    "Maven.spark-image-repo [compile].executor": "Run",
    "Maven.spark-image-repo [install].executor": "Run",
    "Maven.spark-image-repo [spring-boot:run].executor": "Run",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "kotlin-language-version-configured": "true"
  }
}]]></component>
  <component name="RunAnythingCache">
    <option name="myCommands">
      <command value="mvn compile" />
      <command value="mvn clean" />
      <command value="mvn install" />
      <command value="mvn spring-boot:run" />
    </option>
  </component>
  <component name="RunManager">
    <configuration name="SparkImageRepoApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.matrimony.sparkimagerepo.SparkImageRepoApplication" />
      <module name="spark-image-repo" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.matrimony.sparkimagerepo.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="JetRunConfigurationType">
      <module name="SparkImageRepo" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="SparkImageRepo" />
      <option name="filePath" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.SparkImageRepoApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="8a4b40a1-f069-4ffe-83a0-38521e22717f" name="Changes" comment="" />
      <created>1752235739724</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752235739724</updated>
    </task>
    <servers />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/com/matrimony/sparkimagerepo/controller/ImageUploadController.java</url>
          <line>24</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>