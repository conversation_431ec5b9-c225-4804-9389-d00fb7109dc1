package com.matrimony.sparkimagerepo.dto;

import com.matrimony.sparkimagerepo.entity.ImageTransaction;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserImagesResponse {

    private Long userId;
    private List<ImageInfo> images;
    private int totalImages;
    private String status;
    private String message;

    // Constructors
    public UserImagesResponse() {}

    public UserImagesResponse(Long userId, List<ImageTransaction> transactions) {
        this.userId = userId;
        this.images = transactions.stream()
            .map(ImageInfo::fromTransaction)
            .collect(Collectors.toList());
        this.totalImages = images.size();
        this.status = "SUCCESS";
        this.message = "Images retrieved successfully";
    }

    // Getters and Setters
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public List<ImageInfo> getImages() {
        return images;
    }

    public void setImages(List<ImageInfo> images) {
        this.images = images;
    }

    public int getTotalImages() {
        return totalImages;
    }

    public void setTotalImages(int totalImages) {
        this.totalImages = totalImages;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    // Nested class for individual image information
    public static class ImageInfo {
        private String transactionId;
        private String cloudinaryPublicId;
        private String originalFileName;
        private ImageUploadResponse.ImageUrls urls;
        private String status;
        private LocalDateTime uploadedAt;
        private Long fileSizeBytes;

        public ImageInfo() {}

        public static ImageInfo fromTransaction(ImageTransaction transaction) {
            ImageInfo info = new ImageInfo();
            info.transactionId = transaction.getId();
            info.cloudinaryPublicId = transaction.getCloudinaryPublicId();
            info.originalFileName = transaction.getOriginalFileName();
            info.urls = new ImageUploadResponse.ImageUrls(
                transaction.getThumbnailUrl(),
                transaction.getSliderUrl(),
                transaction.getOriginalUrl()
            );
            info.status = transaction.getStatus().name();
            info.uploadedAt = transaction.getCreatedAt();
            info.fileSizeBytes = transaction.getFileSizeAfter() != null ? 
                transaction.getFileSizeAfter() : transaction.getFileSizeBefore();
            return info;
        }

        // Getters and Setters
        public String getTransactionId() {
            return transactionId;
        }

        public void setTransactionId(String transactionId) {
            this.transactionId = transactionId;
        }

        public String getCloudinaryPublicId() {
            return cloudinaryPublicId;
        }

        public void setCloudinaryPublicId(String cloudinaryPublicId) {
            this.cloudinaryPublicId = cloudinaryPublicId;
        }

        public String getOriginalFileName() {
            return originalFileName;
        }

        public void setOriginalFileName(String originalFileName) {
            this.originalFileName = originalFileName;
        }

        public ImageUploadResponse.ImageUrls getUrls() {
            return urls;
        }

        public void setUrls(ImageUploadResponse.ImageUrls urls) {
            this.urls = urls;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public LocalDateTime getUploadedAt() {
            return uploadedAt;
        }

        public void setUploadedAt(LocalDateTime uploadedAt) {
            this.uploadedAt = uploadedAt;
        }

        public Long getFileSizeBytes() {
            return fileSizeBytes;
        }

        public void setFileSizeBytes(Long fileSizeBytes) {
            this.fileSizeBytes = fileSizeBytes;
        }
    }
}
