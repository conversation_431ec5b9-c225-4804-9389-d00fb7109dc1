package com.matrimony.sparkimagerepo.service;

import com.cloudinary.Cloudinary;
import com.cloudinary.Transformation;
import com.cloudinary.utils.ObjectUtils;
import com.matrimony.sparkimagerepo.dto.ImageUploadRequest;
import com.matrimony.sparkimagerepo.dto.ImageUploadResponse;
import com.matrimony.sparkimagerepo.dto.UserImagesResponse;
import com.matrimony.sparkimagerepo.entity.ImageTransaction;
import com.matrimony.sparkimagerepo.exception.ImageProcessingException;
import com.matrimony.sparkimagerepo.exception.InvalidImageFormatException;
import com.matrimony.sparkimagerepo.repository.ImageTransactionRepository;
import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.UUID;

@Service
public class ImageUploadService {

    private static final Logger logger = LoggerFactory.getLogger(ImageUploadService.class);

    @Autowired
    private Cloudinary cloudinary;

    @Autowired
    private ImageTransactionRepository imageTransactionRepository;

    @Value("${image.max-size-mb:10}")
    private int maxSizeMB;

    @Value("${image.allowed-formats:jpg,jpeg,png,webp}")
    private String allowedFormats;

    @Value("${image.compression.quality:85}")
    private int compressionQuality;

    @Value("${image.compression.max-width:2048}")
    private int maxWidth;

    @Value("${image.compression.max-height:2048}")
    private int maxHeight;

    @Value("${image.transformations.thumbnail.width:150}")
    private int thumbnailWidth;

    @Value("${image.transformations.thumbnail.height:150}")
    private int thumbnailHeight;

    @Value("${image.transformations.slider.width:1024}")
    private int sliderWidth;

    @Value("${image.transformations.slider.height:768}")
    private int sliderHeight;

    @Value("${app.upload.retry-attempts:3}")
    private int retryAttempts;

    @Value("${app.upload.retry-delay-ms:1000}")
    private long retryDelayMs;

    /**
     * Upload image with validation, compression, and Cloudinary integration
     */
    @Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public ImageUploadResponse uploadImage(ImageUploadRequest request) {
        long startTime = System.currentTimeMillis();
        ImageTransaction transaction = null;

        try {
            // Create initial transaction record
            transaction = new ImageTransaction(request.getUserId(), request.getImage().getOriginalFilename());
            transaction.setContentType(request.getImage().getContentType());
            transaction.setFileSizeBefore(request.getImage().getSize());
            transaction.setStatus(ImageTransaction.TransactionStatus.PROCESSING);
            transaction = imageTransactionRepository.save(transaction);

            logger.info("Starting image upload for user: {}, file: {}, transaction: {}", 
                request.getUserId(), request.getImage().getOriginalFilename(), transaction.getId());

            // Validate image
            validateImage(request.getImage());

            // Process image (compress if needed)
            byte[] processedImageData = processImage(request.getImage());
            transaction.setFileSizeAfter((long) processedImageData.length);

            // Upload to Cloudinary
            Map<String, Object> uploadResult = uploadToCloudinary(processedImageData, request);
            String publicId = (String) uploadResult.get("public_id");
            transaction.setCloudinaryPublicId(publicId);

            // Generate transformation URLs
            generateTransformationUrls(transaction, publicId);

            // Update transaction status
            long duration = System.currentTimeMillis() - startTime;
            transaction.setUploadDurationMs(duration);
            transaction.setStatus(ImageTransaction.TransactionStatus.SUCCESS);
            transaction = imageTransactionRepository.save(transaction);

            logger.info("Image upload completed successfully for transaction: {} in {}ms", 
                transaction.getId(), duration);

            return ImageUploadResponse.success(transaction);

        } catch (Exception e) {
            logger.error("Image upload failed for user: {}, file: {}", 
                request.getUserId(), request.getImage().getOriginalFilename(), e);

            if (transaction != null) {
                transaction.setStatus(ImageTransaction.TransactionStatus.FAILED);
                transaction.setErrorMessage(e.getMessage());
                transaction.setUploadDurationMs(System.currentTimeMillis() - startTime);
                imageTransactionRepository.save(transaction);
            }

            return ImageUploadResponse.failure(e.getMessage());
        }
    }

    /**
     * Get all images for a specific user
     */
    public UserImagesResponse getUserImages(Long userId, int page, int size) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<ImageTransaction> transactions = imageTransactionRepository
                .findByUserIdAndStatusOrderByCreatedAtDesc(
                    userId, 
                    ImageTransaction.TransactionStatus.SUCCESS, 
                    pageable
                );

            return new UserImagesResponse(userId, transactions.getContent());

        } catch (Exception e) {
            logger.error("Failed to retrieve images for user: {}", userId, e);
            UserImagesResponse response = new UserImagesResponse();
            response.setUserId(userId);
            response.setStatus("FAILED");
            response.setMessage("Failed to retrieve images: " + e.getMessage());
            return response;
        }
    }

    /**
     * Get all images for a specific user (without pagination)
     */
    public UserImagesResponse getUserImages(Long userId) {
        try {
            List<ImageTransaction> transactions = imageTransactionRepository
                .findByUserIdAndStatusOrderByCreatedAtDesc(
                    userId, 
                    ImageTransaction.TransactionStatus.SUCCESS
                );

            return new UserImagesResponse(userId, transactions);

        } catch (Exception e) {
            logger.error("Failed to retrieve images for user: {}", userId, e);
            UserImagesResponse response = new UserImagesResponse();
            response.setUserId(userId);
            response.setStatus("FAILED");
            response.setMessage("Failed to retrieve images: " + e.getMessage());
            return response;
        }
    }

    /**
     * Validate uploaded image
     */
    private void validateImage(MultipartFile file) {
        // Check if file is empty
        if (file.isEmpty()) {
            throw new InvalidImageFormatException("Image file is empty");
        }

        // Check file size
        long fileSizeBytes = file.getSize();
        long maxSizeBytes = maxSizeMB * 1024L * 1024L;
        if (fileSizeBytes > maxSizeBytes) {
            throw new InvalidImageFormatException(
                String.format("File size %d bytes exceeds maximum allowed size %d MB", 
                    fileSizeBytes, maxSizeMB));
        }

        // Check file format
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null) {
            throw new InvalidImageFormatException("Original filename is null");
        }

        String extension = FilenameUtils.getExtension(originalFilename).toLowerCase();
        List<String> allowedExtensions = Arrays.asList(allowedFormats.split(","));
        if (!allowedExtensions.contains(extension)) {
            throw new InvalidImageFormatException(
                String.format("File format '%s' is not allowed. Allowed formats: %s", 
                    extension, allowedFormats));
        }

        // Validate content type
        String contentType = file.getContentType();
        if (contentType == null || !contentType.startsWith("image/")) {
            throw new InvalidImageFormatException("Invalid content type: " + contentType);
        }

        logger.debug("Image validation passed for file: {}", originalFilename);
    }

    /**
     * Process image - compress if larger than 10MB
     */
    private byte[] processImage(MultipartFile file) throws IOException {
        byte[] originalData = file.getBytes();
        
        // If file is already under 10MB, return as is
        if (originalData.length <= maxSizeMB * 1024 * 1024) {
            logger.debug("Image size is within limits, no compression needed");
            return originalData;
        }

        logger.info("Image size {} bytes exceeds limit, compressing...", originalData.length);

        try {
            // Read image
            BufferedImage originalImage = ImageIO.read(new ByteArrayInputStream(originalData));
            if (originalImage == null) {
                throw new ImageProcessingException("Unable to read image data");
            }

            // Calculate new dimensions while maintaining aspect ratio
            Dimension newDimensions = calculateNewDimensions(
                originalImage.getWidth(), 
                originalImage.getHeight()
            );

            // Resize image
            BufferedImage resizedImage = resizeImage(originalImage, newDimensions);

            // Compress and convert to bytes
            byte[] compressedData = compressImage(resizedImage, file.getContentType());

            logger.info("Image compressed from {} bytes to {} bytes", 
                originalData.length, compressedData.length);

            return compressedData;

        } catch (Exception e) {
            logger.error("Image processing failed, using original", e);
            throw new ImageProcessingException("Failed to process image: " + e.getMessage());
        }
    }

    /**
     * Calculate new dimensions while maintaining aspect ratio
     */
    private Dimension calculateNewDimensions(int originalWidth, int originalHeight) {
        double aspectRatio = (double) originalWidth / originalHeight;

        int newWidth = originalWidth;
        int newHeight = originalHeight;

        if (originalWidth > maxWidth) {
            newWidth = maxWidth;
            newHeight = (int) (newWidth / aspectRatio);
        }

        if (newHeight > maxHeight) {
            newHeight = maxHeight;
            newWidth = (int) (newHeight * aspectRatio);
        }

        return new Dimension(newWidth, newHeight);
    }

    /**
     * Resize image using high-quality scaling
     */
    private BufferedImage resizeImage(BufferedImage originalImage, Dimension newDimensions) {
        int newWidth = (int) newDimensions.getWidth();
        int newHeight = (int) newDimensions.getHeight();

        BufferedImage resizedImage = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = resizedImage.createGraphics();

        // Set high-quality rendering hints
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

        g2d.drawImage(originalImage, 0, 0, newWidth, newHeight, null);
        g2d.dispose();

        return resizedImage;
    }

    /**
     * Compress image to bytes
     */
    private byte[] compressImage(BufferedImage image, String contentType) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();

        String formatName = "jpg"; // Default to JPEG for better compression
        if (contentType != null && contentType.contains("png")) {
            formatName = "png";
        }

        ImageIO.write(image, formatName, baos);
        return baos.toByteArray();
    }

    /**
     * Upload processed image to Cloudinary
     */
    private Map<String, Object> uploadToCloudinary(byte[] imageData, ImageUploadRequest request) {
        try {
            String publicId = generatePublicId(request);

            Map<String, Object> uploadParams = ObjectUtils.asMap(
                "public_id", publicId,
                "folder", "matrimony/users/" + request.getUserId(),
                "resource_type", "image",
                "quality", "auto",
                "fetch_format", "auto",
                "overwrite", false,
                "unique_filename", true
            );

            if (request.getCategory() != null) {
                uploadParams.put("tags", Arrays.asList("matrimony", request.getCategory()));
            }

            Map<String, Object> result = cloudinary.uploader().upload(imageData, uploadParams);
            logger.debug("Cloudinary upload successful: {}", result.get("public_id"));

            return result;

        } catch (Exception e) {
            logger.error("Cloudinary upload failed", e);
            throw new ImageProcessingException("Failed to upload to Cloudinary: " + e.getMessage());
        }
    }

    /**
     * Generate unique public ID for Cloudinary
     */
    private String generatePublicId(ImageUploadRequest request) {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String uuid = UUID.randomUUID().toString().substring(0, 8);
        String category = request.getCategory() != null ? request.getCategory() : "general";

        return String.format("user_%d_%s_%s_%s",
            request.getUserId(), category, timestamp, uuid);
    }

    /**
     * Generate transformation URLs for different use cases
     */
    private void generateTransformationUrls(ImageTransaction transaction, String publicId) {
        try {
            // Thumbnail URL (150x150, cropped, face detection)
            String thumbnailUrl = cloudinary.url()
                    .transformation(new Transformation()
                            .width(thumbnailWidth)
                            .height(thumbnailHeight)
                            .crop("fill")
                            .gravity("face")
                            .quality("auto")
                            .fetchFormat("auto"))
                    .generate(publicId);

            // Slider URL (1024x768, responsive)
            String sliderUrl = cloudinary.url()
                    .transformation(new Transformation()
                            .width(sliderWidth)
                            .height(sliderHeight)
                            .crop("limit")
                            .quality("auto")
                            .fetchFormat("auto"))
                    .generate(publicId);

            // Original URL (optimized but full size)
            String originalUrl = cloudinary.url()
                    .transformation(new Transformation()
                            .quality("auto")
                            .fetchFormat("auto"))
                    .generate(publicId);

            transaction.setThumbnailUrl(thumbnailUrl);
            transaction.setSliderUrl(sliderUrl);
            transaction.setOriginalUrl(originalUrl);

            logger.debug("Generated transformation URLs for public_id: {}", publicId);

        } catch (Exception e) {
            logger.error("Failed to generate transformation URLs for public_id: {}", publicId, e);
            throw new ImageProcessingException("Failed to generate image URLs: " + e.getMessage());
        }
    }

    /**
     * Retry failed uploads
     */
    @Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    public void retryFailedUploads() {
        LocalDateTime retryWindow = LocalDateTime.now().minusHours(24);
        List<ImageTransaction> failedTransactions = imageTransactionRepository
            .findTransactionsForRetry(retryWindow);

        logger.info("Found {} failed transactions to retry", failedTransactions.size());

        for (ImageTransaction transaction : failedTransactions) {
            try {
                // Implement retry logic here if needed
                logger.info("Retrying transaction: {}", transaction.getId());
                // This would require storing original request data or implementing a different approach
            } catch (Exception e) {
                logger.error("Retry failed for transaction: {}", transaction.getId(), e);
            }
        }
    }
}
