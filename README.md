# Spark Image Repository - Matrimony API

A Spring Boot REST API that integrates with Cloudinary for secure image upload, processing, and management for matrimony applications.

## Features

✅ **Secure Image Upload**: Multipart/form-data upload with backend validation  
✅ **Automatic Compression**: Files >10MB are automatically compressed and resized  
✅ **Multiple Transformations**: Thumbnail (150x150), Slider (1024x768), and Original URLs  
✅ **Cloudinary Integration**: Leverages Cloudinary's powerful image processing capabilities  
✅ **Database Logging**: Complete transaction logging with status tracking  
✅ **REST API**: Well-structured JSON responses following REST best practices  
✅ **Retry Logic**: Automatic retry for failed uploads  
✅ **CORS Support**: Configured for frontend integration

## Tech Stack

- **Java 17+**
- **Spring Boot 3.2.0**
- **Spring Data MongoDB**
- **MongoDB** (Local for dev/test, Atlas for production)
- **Cloudinary Java SDK**
- **Maven**

## Quick Start

### Prerequisites

1. **Java 17+** installed
2. **MongoDB** installed locally (for development)
3. **MongoDB Atlas account** (for production - free tier available)
4. **Cloudinary account** (free tier available)
5. **Maven** installed

### 1. Clone and Setup

```bash
git clone <repository-url>
cd SparkImageRepo
```

### 2. Database Setup

#### For Development (Local MongoDB)

Install MongoDB locally:

**Windows:**

```bash
# Download and install MongoDB Community Server from mongodb.com
# Start MongoDB service
net start MongoDB
```

**macOS:**

```bash
brew install mongodb-community
brew services start mongodb-community
```

**Linux:**

```bash
# Install MongoDB following official documentation
sudo systemctl start mongod
sudo systemctl enable mongod
```

#### For Production (MongoDB Atlas)

1. Create account at [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Create a new cluster (free tier available)
3. Create a database user
4. Get your connection string
5. Whitelist your IP address

### 3. Cloudinary Setup

1. Sign up at [Cloudinary](https://cloudinary.com/)
2. Get your credentials from the dashboard
3. Set environment variables:

```bash
export CLOUDINARY_CLOUD_NAME=your_cloud_name
export CLOUDINARY_API_KEY=your_api_key
export CLOUDINARY_API_SECRET=your_api_secret
```

### 4. Configure Application

#### For Development (Local MongoDB)

The default configuration in `application.yml` uses local MongoDB:

```yaml
spring:
  data:
    mongodb:
      uri: mongodb://localhost:27017/matrimony_db
      database: matrimony_db

cloudinary:
  cloud-name: ${CLOUDINARY_CLOUD_NAME}
  api-key: ${CLOUDINARY_API_KEY}
  api-secret: ${CLOUDINARY_API_SECRET}
```

#### For Production (MongoDB Atlas)

Set environment variables:

```bash
export MONGODB_ATLAS_URI="mongodb+srv://username:<EMAIL>/matrimony_prod_db?retryWrites=true&w=majority"
export MONGODB_DATABASE=matrimony_prod_db
export SPRING_PROFILES_ACTIVE=prod
```

### 5. Run the Application

#### Development Mode

```bash
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

#### Production Mode

```bash
mvn spring-boot:run -Dspring-boot.run.profiles=prod
```

The API will be available at: `http://localhost:8080/api/v1`

## API Endpoints

### Upload Image

```bash
POST /api/v1/images/upload
Content-Type: multipart/form-data

Parameters:
- userId (required): User ID
- image (required): Image file
- description (optional): Image description
- category (optional): Image category
```

### Get User Images

```bash
GET /api/v1/images/user/{userId}?page=0&size=20
```

### Get All User Images

```bash
GET /api/v1/images/user/{userId}/all
```

## Sample Usage

### cURL Example

```bash
# Upload an image
curl -X POST http://localhost:8080/api/v1/images/upload \
  -F "userId=12345" \
  -F "image=@profile.jpg" \
  -F "description=Profile picture" \
  -F "category=profile"

# Get user images
curl -X GET "http://localhost:8080/api/v1/images/user/12345?page=0&size=10"
```

### React Frontend Example

```javascript
const uploadImage = async (userId, imageFile) => {
  const formData = new FormData();
  formData.append("userId", userId);
  formData.append("image", imageFile);

  const response = await fetch("/api/v1/images/upload", {
    method: "POST",
    body: formData,
  });

  return await response.json();
};
```

## Database Schema

The `image_transactions` table automatically tracks:

- User ID and file details
- Cloudinary public ID and URLs
- File sizes (before/after compression)
- Upload status and duration
- Error messages and timestamps

## Configuration Options

Key settings in `application.yml`:

```yaml
image:
  max-size-mb: 10 # Maximum file size
  allowed-formats: jpg,jpeg,png,webp # Supported formats
  compression:
    quality: 85 # Compression quality
    max-width: 2048 # Max width after compression
    max-height: 2048 # Max height after compression
  transformations:
    thumbnail:
      width: 150
      height: 150
    slider:
      width: 1024
      height: 768
```

## Production Deployment

### Environment Variables

```bash
# MongoDB Atlas
MONGODB_ATLAS_URI=mongodb+srv://username:<EMAIL>/matrimony_prod_db?retryWrites=true&w=majority
MONGODB_DATABASE=matrimony_prod_db

# Cloudinary
CLOUDINARY_CLOUD_NAME=your_production_cloud
CLOUDINARY_API_KEY=your_production_key
CLOUDINARY_API_SECRET=your_production_secret

# CORS
CORS_ALLOWED_ORIGINS=https://yourdomain.com

# Profile
SPRING_PROFILES_ACTIVE=prod
```

### Docker Deployment

```dockerfile
FROM openjdk:17-jdk-slim
COPY target/spark-image-repo-0.0.1-SNAPSHOT.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

## Testing

Run tests with:

```bash
mvn test
```

## Monitoring

Health check endpoint:

```bash
GET /api/v1/images/health
```

Actuator endpoints (if enabled):

```bash
GET /actuator/health
GET /actuator/metrics
```

## Troubleshooting

### Common Issues

1. **Cloudinary Connection Failed**

   - Verify your Cloudinary credentials
   - Check network connectivity

2. **Database Connection Issues**

   - Ensure PostgreSQL is running
   - Verify database credentials and URL

3. **File Upload Errors**

   - Check file size limits
   - Verify supported file formats

4. **CORS Issues**
   - Update `CORS_ALLOWED_ORIGINS` in configuration

### Logs

Check application logs in:

- Console output
- `logs/spark-image-repo.log` (if file logging enabled)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For issues and questions:

- Check the API documentation
- Review the troubleshooting section
- Create an issue in the repository
