package com.matrimony.sparkimagerepo.config;

import com.matrimony.sparkimagerepo.entity.ImageTransaction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.Index;
import org.springframework.data.mongodb.core.index.IndexOperations;

import jakarta.annotation.PostConstruct;

@Configuration
public class MongoConfig {

    @Autowired
    private MongoTemplate mongoTemplate;

    @PostConstruct
    public void initIndexes() {
        IndexOperations indexOps = mongoTemplate.indexOps(ImageTransaction.class);

        // Index on userId for fast user queries
        indexOps.ensureIndex(new Index().on("userId", Sort.Direction.ASC).named("idx_user_id"));

        // Index on status for filtering by status
        indexOps.ensureIndex(new Index().on("status", Sort.Direction.ASC).named("idx_status"));

        // Index on createdAt for time-based queries and sorting
        indexOps.ensureIndex(new Index().on("createdAt", Sort.Direction.DESC).named("idx_created_at"));

        // Compound index on userId and status for efficient user-specific status queries
        indexOps.ensureIndex(new Index()
            .on("userId", Sort.Direction.ASC)
            .on("status", Sort.Direction.ASC)
            .named("idx_user_status"));

        // Compound index on userId and createdAt for user timeline queries
        indexOps.ensureIndex(new Index()
            .on("userId", Sort.Direction.ASC)
            .on("createdAt", Sort.Direction.DESC)
            .named("idx_user_created"));

        // Index on cloudinaryPublicId for unique lookups
        indexOps.ensureIndex(new Index()
            .on("cloudinaryPublicId", Sort.Direction.ASC)
            .unique()
            .named("idx_cloudinary_public_id"));

        // Compound index for retry queries (status + createdAt)
        indexOps.ensureIndex(new Index()
            .on("status", Sort.Direction.ASC)
            .on("createdAt", Sort.Direction.ASC)
            .named("idx_status_created"));

        // TTL index for automatic cleanup of old failed transactions (optional)
        // Uncomment if you want automatic cleanup after 30 days
        /*
        indexOps.ensureIndex(new Index()
            .on("createdAt", Sort.Direction.ASC)
            .expire(Duration.ofDays(30))
            .partial(PartialIndexFilter.of(Criteria.where("status").is("FAILED")))
            .named("idx_ttl_failed"));
        */
    }
}
