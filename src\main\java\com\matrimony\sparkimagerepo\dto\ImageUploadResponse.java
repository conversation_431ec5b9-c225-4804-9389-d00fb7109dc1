package com.matrimony.sparkimagerepo.dto;

import com.matrimony.sparkimagerepo.entity.ImageTransaction;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.time.LocalDateTime;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ImageUploadResponse {

    private String transactionId;
    private String cloudinaryPublicId;
    private ImageUrls urls;
    private String status;
    private String message;
    private FileSizeInfo fileSizeInfo;
    private Long uploadDurationMs;
    private LocalDateTime timestamp;
    private String errorMessage;

    // Constructors
    public ImageUploadResponse() {}

    public ImageUploadResponse(String status, String message) {
        this.status = status;
        this.message = message;
        this.timestamp = LocalDateTime.now();
    }

    // Static factory methods
    public static ImageUploadResponse success(ImageTransaction transaction) {
        ImageUploadResponse response = new ImageUploadResponse();
        response.transactionId = transaction.getId();
        response.cloudinaryPublicId = transaction.getCloudinaryPublicId();
        response.urls = new ImageUrls(
            transaction.getThumbnailUrl(),
            transaction.getSliderUrl(),
            transaction.getOriginalUrl()
        );
        response.status = "SUCCESS";
        response.message = "Image uploaded successfully";
        response.fileSizeInfo = new FileSizeInfo(
            transaction.getFileSizeBefore(),
            transaction.getFileSizeAfter()
        );
        response.uploadDurationMs = transaction.getUploadDurationMs();
        response.timestamp = transaction.getCreatedAt();
        return response;
    }

    public static ImageUploadResponse failure(String errorMessage) {
        ImageUploadResponse response = new ImageUploadResponse();
        response.status = "FAILED";
        response.message = "Image upload failed";
        response.errorMessage = errorMessage;
        response.timestamp = LocalDateTime.now();
        return response;
    }

    public static ImageUploadResponse processing(String transactionId) {
        ImageUploadResponse response = new ImageUploadResponse();
        response.transactionId = transactionId;
        response.status = "PROCESSING";
        response.message = "Image upload is being processed";
        response.timestamp = LocalDateTime.now();
        return response;
    }

    // Getters and Setters
    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public String getCloudinaryPublicId() {
        return cloudinaryPublicId;
    }

    public void setCloudinaryPublicId(String cloudinaryPublicId) {
        this.cloudinaryPublicId = cloudinaryPublicId;
    }

    public ImageUrls getUrls() {
        return urls;
    }

    public void setUrls(ImageUrls urls) {
        this.urls = urls;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public FileSizeInfo getFileSizeInfo() {
        return fileSizeInfo;
    }

    public void setFileSizeInfo(FileSizeInfo fileSizeInfo) {
        this.fileSizeInfo = fileSizeInfo;
    }

    public Long getUploadDurationMs() {
        return uploadDurationMs;
    }

    public void setUploadDurationMs(Long uploadDurationMs) {
        this.uploadDurationMs = uploadDurationMs;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    // Nested classes
    public static class ImageUrls {
        private String thumbnail;
        private String slider;
        private String original;

        public ImageUrls() {}

        public ImageUrls(String thumbnail, String slider, String original) {
            this.thumbnail = thumbnail;
            this.slider = slider;
            this.original = original;
        }

        // Getters and Setters
        public String getThumbnail() {
            return thumbnail;
        }

        public void setThumbnail(String thumbnail) {
            this.thumbnail = thumbnail;
        }

        public String getSlider() {
            return slider;
        }

        public void setSlider(String slider) {
            this.slider = slider;
        }

        public String getOriginal() {
            return original;
        }

        public void setOriginal(String original) {
            this.original = original;
        }
    }

    public static class FileSizeInfo {
        private Long originalSizeBytes;
        private Long processedSizeBytes;
        private String compressionRatio;

        public FileSizeInfo() {}

        public FileSizeInfo(Long originalSizeBytes, Long processedSizeBytes) {
            this.originalSizeBytes = originalSizeBytes;
            this.processedSizeBytes = processedSizeBytes;
            if (originalSizeBytes != null && processedSizeBytes != null && originalSizeBytes > 0) {
                double ratio = ((double) (originalSizeBytes - processedSizeBytes) / originalSizeBytes) * 100;
                this.compressionRatio = String.format("%.1f%%", ratio);
            }
        }

        // Getters and Setters
        public Long getOriginalSizeBytes() {
            return originalSizeBytes;
        }

        public void setOriginalSizeBytes(Long originalSizeBytes) {
            this.originalSizeBytes = originalSizeBytes;
        }

        public Long getProcessedSizeBytes() {
            return processedSizeBytes;
        }

        public void setProcessedSizeBytes(Long processedSizeBytes) {
            this.processedSizeBytes = processedSizeBytes;
        }

        public String getCompressionRatio() {
            return compressionRatio;
        }

        public void setCompressionRatio(String compressionRatio) {
            this.compressionRatio = compressionRatio;
        }
    }
}
