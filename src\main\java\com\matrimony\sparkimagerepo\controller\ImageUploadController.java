package com.matrimony.sparkimagerepo.controller;

import com.matrimony.sparkimagerepo.dto.ImageUploadRequest;
import com.matrimony.sparkimagerepo.dto.ImageUploadResponse;
import com.matrimony.sparkimagerepo.dto.UserImagesResponse;
import com.matrimony.sparkimagerepo.exception.ImageProcessingException;
import com.matrimony.sparkimagerepo.exception.InvalidImageFormatException;
import com.matrimony.sparkimagerepo.service.ImageUploadService;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@Validated
@CrossOrigin(origins = {"${app.cors.allowed-origins}"})
public class ImageUploadController {

    private static final Logger logger = LoggerFactory.getLogger(ImageUploadController.class);

    @Autowired
    private ImageUploadService imageUploadService;

    /**
     * Upload image endpoint
     * POST /api/v1/images/upload
     */
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ImageUploadResponse> uploadImage(
            @RequestParam("userId") @NotNull @Positive Long userId,
            @RequestParam("image") @NotNull MultipartFile image,
            @RequestParam(value = "description", required = false) String description,
            @RequestParam(value = "category", required = false) String category) {

        try {
            logger.info("Received image upload request for user: {}, file: {}", 
                userId, image.getOriginalFilename());

            // Create request DTO
            ImageUploadRequest request = new ImageUploadRequest(userId, image, description, category);

            // Process upload
            ImageUploadResponse response = imageUploadService.uploadImage(request);

            // Return appropriate HTTP status based on response
            HttpStatus status = "SUCCESS".equals(response.getStatus()) ? 
                HttpStatus.CREATED : HttpStatus.BAD_REQUEST;

            return ResponseEntity.status(status).body(response);

        } catch (InvalidImageFormatException e) {
            logger.warn("Invalid image format for user: {}, error: {}", userId, e.getMessage());
            return ResponseEntity.badRequest()
                .body(ImageUploadResponse.failure("Invalid image format: " + e.getMessage()));

        } catch (ImageProcessingException e) {
            logger.error("Image processing failed for user: {}, error: {}", userId, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ImageUploadResponse.failure("Image processing failed: " + e.getMessage()));

        } catch (Exception e) {
            logger.error("Unexpected error during image upload for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ImageUploadResponse.failure("Upload failed: " + e.getMessage()));
        }
    }

    /**
     * Get all images for a specific user
     * GET /api/v1/images/user/{userId}
     */
    @GetMapping("/user/{userId}")
    public ResponseEntity<UserImagesResponse> getUserImages(
            @PathVariable @NotNull @Positive Long userId,
            @RequestParam(value = "page", defaultValue = "0") @Min(0) int page,
            @RequestParam(value = "size", defaultValue = "20") @Min(1) int size) {

        try {
            logger.info("Retrieving images for user: {}, page: {}, size: {}", userId, page, size);

            UserImagesResponse response = imageUploadService.getUserImages(userId, page, size);

            HttpStatus status = "SUCCESS".equals(response.getStatus()) ? 
                HttpStatus.OK : HttpStatus.INTERNAL_SERVER_ERROR;

            return ResponseEntity.status(status).body(response);

        } catch (Exception e) {
            logger.error("Failed to retrieve images for user: {}", userId, e);
            
            UserImagesResponse errorResponse = new UserImagesResponse();
            errorResponse.setUserId(userId);
            errorResponse.setStatus("FAILED");
            errorResponse.setMessage("Failed to retrieve images: " + e.getMessage());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * Get all images for a specific user (without pagination)
     * GET /api/v1/images/user/{userId}/all
     */
    @GetMapping("/user/{userId}/all")
    public ResponseEntity<UserImagesResponse> getAllUserImages(
            @PathVariable @NotNull @Positive Long userId) {

        try {
            logger.info("Retrieving all images for user: {}", userId);

            UserImagesResponse response = imageUploadService.getUserImages(userId);

            HttpStatus status = "SUCCESS".equals(response.getStatus()) ? 
                HttpStatus.OK : HttpStatus.INTERNAL_SERVER_ERROR;

            return ResponseEntity.status(status).body(response);

        } catch (Exception e) {
            logger.error("Failed to retrieve all images for user: {}", userId, e);
            
            UserImagesResponse errorResponse = new UserImagesResponse();
            errorResponse.setUserId(userId);
            errorResponse.setStatus("FAILED");
            errorResponse.setMessage("Failed to retrieve images: " + e.getMessage());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    /**
     * Health check endpoint
     * GET /api/v1/images/health
     */
    @GetMapping("/health")
    public ResponseEntity<String> healthCheck() {
    return ResponseEntity.ok("Image Upload Service is running");
    }
    /**
     * Global exception handler for validation errors
     */
    @ExceptionHandler(org.springframework.web.bind.MethodArgumentNotValidException.class)
    public ResponseEntity<ImageUploadResponse> handleValidationErrors(
            org.springframework.web.bind.MethodArgumentNotValidException ex) {
        
        String errorMessage = ex.getBindingResult().getFieldErrors().stream()
            .map(error -> error.getField() + ": " + error.getDefaultMessage())
            .reduce((msg1, msg2) -> msg1 + ", " + msg2)
            .orElse("Validation failed");

        logger.warn("Validation error: {}", errorMessage);
        return ResponseEntity.badRequest()
            .body(ImageUploadResponse.failure("Validation failed: " + errorMessage));
    }

    /**
     * Global exception handler for constraint violations
     */
    @ExceptionHandler(jakarta.validation.ConstraintViolationException.class)
    public ResponseEntity<ImageUploadResponse> handleConstraintViolation(
            jakarta.validation.ConstraintViolationException ex) {
        
        String errorMessage = ex.getConstraintViolations().stream()
            .map(violation -> violation.getPropertyPath() + ": " + violation.getMessage())
            .reduce((msg1, msg2) -> msg1 + ", " + msg2)
            .orElse("Constraint violation");

        logger.warn("Constraint violation: {}", errorMessage);
        return ResponseEntity.badRequest()
            .body(ImageUploadResponse.failure("Invalid request: " + errorMessage));
    }
}
