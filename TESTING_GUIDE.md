# Testing Guide for Spark Image Repository API

## Overview

This guide provides comprehensive testing strategies for the Spark Image Repository API, including unit tests, integration tests, and manual testing approaches.

## Test Structure

```
src/test/java/com/matrimony/sparkimagerepo/
├── controller/          # Controller layer tests
├── service/            # Service layer tests
├── repository/         # Repository layer tests
├── integration/        # Integration tests
└── util/              # Test utilities
```

## Running Tests

### All Tests
```bash
mvn test
```

### Specific Test Class
```bash
mvn test -Dtest=ImageUploadServiceTest
```

### Integration Tests Only
```bash
mvn test -Dtest="*IntegrationTest"
```

## Test Categories

### 1. Unit Tests

#### Service Layer Tests (`ImageUploadServiceTest`)

**Test Cases to Implement:**

```java
@ExtendWith(MockitoExtension.class)
class ImageUploadServiceTest {

    @Mock
    private Cloudinary cloudinary;
    
    @Mock
    private ImageTransactionRepository repository;
    
    @InjectMocks
    private ImageUploadService imageUploadService;

    @Test
    void uploadImage_ValidImage_ReturnsSuccess() {
        // Test successful image upload
    }

    @Test
    void uploadImage_InvalidFormat_ThrowsException() {
        // Test invalid file format handling
    }

    @Test
    void uploadImage_FileTooLarge_CompressesImage() {
        // Test automatic compression for large files
    }

    @Test
    void uploadImage_CloudinaryFailure_ReturnsError() {
        // Test Cloudinary upload failure handling
    }

    @Test
    void getUserImages_ValidUserId_ReturnsImages() {
        // Test retrieving user images
    }

    @Test
    void validateImage_InvalidFormat_ThrowsException() {
        // Test image validation logic
    }
}
```

#### Repository Layer Tests (`ImageTransactionRepositoryTest`)

```java
@DataJpaTest
class ImageTransactionRepositoryTest {

    @Autowired
    private TestEntityManager entityManager;
    
    @Autowired
    private ImageTransactionRepository repository;

    @Test
    void findByUserIdAndStatus_ExistingUser_ReturnsTransactions() {
        // Test finding transactions by user and status
    }

    @Test
    void countByUserIdAndStatus_ExistingUser_ReturnsCount() {
        // Test counting user transactions
    }

    @Test
    void findByCloudinaryPublicId_ExistingId_ReturnsTransaction() {
        // Test finding by Cloudinary public ID
    }
}
```

### 2. Integration Tests

#### Controller Integration Tests (`ImageUploadControllerIntegrationTest`)

```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@ActiveProfiles("test")
class ImageUploadControllerIntegrationTest {

    @Autowired
    private TestRestTemplate restTemplate;
    
    @MockBean
    private Cloudinary cloudinary;

    @Test
    void uploadImage_ValidRequest_ReturnsCreated() {
        // Test complete upload flow
    }

    @Test
    void getUserImages_ValidUserId_ReturnsOk() {
        // Test retrieving user images endpoint
    }

    @Test
    void uploadImage_InvalidFile_ReturnsBadRequest() {
        // Test validation error handling
    }
}
```

### 3. Test Data Setup

#### Test Image Files

Create test image files in `src/test/resources/images/`:

```
src/test/resources/images/
├── valid-small.jpg      # Small valid image (< 1MB)
├── valid-large.jpg      # Large valid image (> 10MB)
├── invalid.txt          # Invalid file type
└── corrupted.jpg        # Corrupted image file
```

#### Test Utilities

```java
@Component
public class TestImageUtils {

    public static MultipartFile createMockImageFile(String filename, long size) {
        return new MockMultipartFile(
            "image",
            filename,
            "image/jpeg",
            new byte[(int) size]
        );
    }

    public static ImageTransaction createTestTransaction(Long userId) {
        ImageTransaction transaction = new ImageTransaction(userId, "test.jpg");
        transaction.setStatus(ImageTransaction.TransactionStatus.SUCCESS);
        transaction.setCloudinaryPublicId("test_public_id");
        return transaction;
    }
}
```

## Manual Testing

### 1. API Testing with cURL

#### Upload Valid Image
```bash
curl -X POST http://localhost:8080/api/v1/images/upload \
  -F "userId=1" \
  -F "image=@test-image.jpg" \
  -F "description=Test upload" \
  -F "category=profile" \
  -v
```

#### Upload Large Image (>10MB)
```bash
curl -X POST http://localhost:8080/api/v1/images/upload \
  -F "userId=1" \
  -F "image=@large-image.jpg" \
  -v
```

#### Upload Invalid Format
```bash
curl -X POST http://localhost:8080/api/v1/images/upload \
  -F "userId=1" \
  -F "image=@document.pdf" \
  -v
```

#### Get User Images
```bash
curl -X GET "http://localhost:8080/api/v1/images/user/1?page=0&size=5" -v
```

### 2. Postman Collection

Create a Postman collection with:

1. **Upload Image** - POST request with form-data
2. **Get User Images** - GET request with query parameters
3. **Get All User Images** - GET request
4. **Health Check** - GET request

### 3. Frontend Testing

#### HTML Test Page

```html
<!DOCTYPE html>
<html>
<head>
    <title>Image Upload Test</title>
</head>
<body>
    <form id="uploadForm" enctype="multipart/form-data">
        <input type="number" name="userId" placeholder="User ID" required>
        <input type="file" name="image" accept="image/*" required>
        <input type="text" name="description" placeholder="Description">
        <input type="text" name="category" placeholder="Category">
        <button type="submit">Upload</button>
    </form>

    <script>
        document.getElementById('uploadForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const formData = new FormData(e.target);
            
            try {
                const response = await fetch('/api/v1/images/upload', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                console.log('Upload result:', result);
                alert(JSON.stringify(result, null, 2));
            } catch (error) {
                console.error('Upload failed:', error);
                alert('Upload failed: ' + error.message);
            }
        });
    </script>
</body>
</html>
```

## Performance Testing

### Load Testing with JMeter

1. Create test plan for concurrent uploads
2. Test with various file sizes
3. Monitor database performance
4. Check Cloudinary API limits

### Stress Testing Scenarios

1. **High Volume Uploads**: 100+ concurrent uploads
2. **Large File Processing**: Multiple 10MB+ files
3. **Database Load**: Thousands of transaction records
4. **Memory Usage**: Monitor heap usage during processing

## Test Environment Setup

### Docker Test Environment

```yaml
version: '3.8'
services:
  postgres-test:
    image: postgres:15
    environment:
      POSTGRES_DB: matrimony_test_db
      POSTGRES_USER: test_user
      POSTGRES_PASSWORD: test_pass
    ports:
      - "5433:5432"
    
  app-test:
    build: .
    environment:
      SPRING_PROFILES_ACTIVE: test
      DB_USERNAME: test_user
      DB_PASSWORD: test_pass
    ports:
      - "8081:8080"
    depends_on:
      - postgres-test
```

### Test Data Cleanup

```java
@TestConfiguration
public class TestDataCleanup {

    @EventListener
    public void cleanupAfterTest(TestExecutionEvent event) {
        // Clean up test data after each test
        // Delete uploaded test images from Cloudinary
        // Clear test database records
    }
}
```

## Continuous Integration

### GitHub Actions Test Workflow

```yaml
name: Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_pass
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-java@v3
        with:
          java-version: '17'
      - run: mvn test
```

## Test Coverage

### Minimum Coverage Targets

- **Service Layer**: 90%+
- **Controller Layer**: 85%+
- **Repository Layer**: 80%+
- **Overall**: 85%+

### Coverage Report

```bash
mvn jacoco:report
```

View report at: `target/site/jacoco/index.html`

## Best Practices

1. **Mock External Dependencies**: Always mock Cloudinary in unit tests
2. **Use Test Profiles**: Separate test configuration from production
3. **Clean Test Data**: Ensure tests don't interfere with each other
4. **Test Edge Cases**: Invalid inputs, network failures, etc.
5. **Performance Assertions**: Include timing assertions for critical paths
6. **Database Transactions**: Use `@Transactional` with rollback for test isolation

## Troubleshooting Tests

### Common Issues

1. **Cloudinary Connection in Tests**: Use `@MockBean` for Cloudinary
2. **Database State**: Ensure proper test data cleanup
3. **File Upload Tests**: Use `MockMultipartFile` for file uploads
4. **Async Operations**: Use `@Async` testing utilities when needed

### Debug Tips

1. Enable debug logging in test profile
2. Use `@Sql` annotations for test data setup
3. Add breakpoints in test methods
4. Check test database state with H2 console
