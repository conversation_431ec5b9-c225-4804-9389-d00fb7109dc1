server:
  port: 8080
  servlet:
    context-path: /api/v1

spring:
  application:
    name: spark-image-repo

  data:
    mongodb:
      uri: ${MONGODB_URI:mongodb://localhost:27017/matrimony_db}
      database: ${MONGODB_DATABASE:matrimony_db}

  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB
      max-request-size: 10MB
      file-size-threshold: 2KB

  jackson:
    property-naming-strategy: SNAKE_CASE
    default-property-inclusion: NON_NULL

# Cloudinary Configuration
cloudinary:
  cloud-name: ${CLOUDINARY_CLOUD_NAME:gosavirepo}
  api-key: ${CLOUDINARY_API_KEY:323311294699186}
  api-secret: ${CLOUDINARY_API_SECRET:-5RAkd7t-VH6gQ6cLSAXzWtlF4I}
  secure: true

# Image Processing Configuration
image:
  max-size-mb: 10
  allowed-formats: jpg,jpeg,png,webp
  compression:
    quality: 85
    max-width: 2048
    max-height: 2048
  transformations:
    thumbnail:
      width: 150
      height: 150
      crop: fill
      gravity: face
    slider:
      width: 1024
      height: 768
      crop: limit
      quality: auto
    original:
      quality: auto
      fetch_format: auto

# Logging Configuration
logging:
  level:
    com.matrimony.sparkimagerepo: DEBUG
    org.springframework.web.multipart: DEBUG
    com.cloudinary: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/spark-image-repo.log

# Management and Monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

# Custom Application Properties
app:
  upload:
    retry-attempts: 3
    retry-delay-ms: 1000
  cors:
    allowed-origins: ${CORS_ALLOWED_ORIGINS:http://localhost:3000,http://localhost:3001}
    allowed-methods: GET,POST,PUT,DELETE,OPTIONS
    allowed-headers: "*"
    allow-credentials: true
