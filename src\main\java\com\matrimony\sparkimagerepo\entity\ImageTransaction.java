package com.matrimony.sparkimagerepo.entity;

import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.index.Indexed;

import java.time.LocalDateTime;

@Document(collection = "image_transactions")
public class ImageTransaction {

    @Id
    private String id;

    @Indexed
    @Field("user_id")
    private Long userId;

    @Field("original_file_name")
    private String originalFileName;

    @Field("cloudinary_public_id")
    private String cloudinaryPublicId;

    @Field("thumbnail_url")
    private String thumbnailUrl;

    @Field("slider_url")
    private String sliderUrl;

    @Field("original_url")
    private String originalUrl;

    @Field("file_size_before")
    private Long fileSizeBefore;

    @Field("file_size_after")
    private Long fileSizeAfter;

    @Indexed
    @Field("status")
    private TransactionStatus status;

    @Field("error_message")
    private String errorMessage;

    @Field("content_type")
    private String contentType;

    @Field("upload_duration_ms")
    private Long uploadDurationMs;

    @CreatedDate
    @Indexed
    @Field("created_at")
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Field("updated_at")
    private LocalDateTime updatedAt;

    // Constructors
    public ImageTransaction() {}

    public ImageTransaction(Long userId, String originalFileName) {
        this.userId = userId;
        this.originalFileName = originalFileName;
        this.status = TransactionStatus.PENDING;
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getOriginalFileName() {
        return originalFileName;
    }

    public void setOriginalFileName(String originalFileName) {
        this.originalFileName = originalFileName;
    }

    public String getCloudinaryPublicId() {
        return cloudinaryPublicId;
    }

    public void setCloudinaryPublicId(String cloudinaryPublicId) {
        this.cloudinaryPublicId = cloudinaryPublicId;
    }

    public String getThumbnailUrl() {
        return thumbnailUrl;
    }

    public void setThumbnailUrl(String thumbnailUrl) {
        this.thumbnailUrl = thumbnailUrl;
    }

    public String getSliderUrl() {
        return sliderUrl;
    }

    public void setSliderUrl(String sliderUrl) {
        this.sliderUrl = sliderUrl;
    }

    public String getOriginalUrl() {
        return originalUrl;
    }

    public void setOriginalUrl(String originalUrl) {
        this.originalUrl = originalUrl;
    }

    public Long getFileSizeBefore() {
        return fileSizeBefore;
    }

    public void setFileSizeBefore(Long fileSizeBefore) {
        this.fileSizeBefore = fileSizeBefore;
    }

    public Long getFileSizeAfter() {
        return fileSizeAfter;
    }

    public void setFileSizeAfter(Long fileSizeAfter) {
        this.fileSizeAfter = fileSizeAfter;
    }

    public TransactionStatus getStatus() {
        return status;
    }

    public void setStatus(TransactionStatus status) {
        this.status = status;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public Long getUploadDurationMs() {
        return uploadDurationMs;
    }

    public void setUploadDurationMs(Long uploadDurationMs) {
        this.uploadDurationMs = uploadDurationMs;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    // Enum for Transaction Status
    public enum TransactionStatus {
        PENDING,
        SUCCESS,
        FAILED,
        PROCESSING
    }
}
