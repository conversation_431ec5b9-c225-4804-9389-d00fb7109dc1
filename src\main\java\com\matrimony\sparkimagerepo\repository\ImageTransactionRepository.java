package com.matrimony.sparkimagerepo.repository;

import com.matrimony.sparkimagerepo.entity.ImageTransaction;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface ImageTransactionRepository extends MongoRepository<ImageTransaction, String> {

    /**
     * Find all successful image transactions for a specific user
     */
    List<ImageTransaction> findByUserIdAndStatusOrderByCreatedAtDesc(
        Long userId, 
        ImageTransaction.TransactionStatus status
    );

    /**
     * Find all image transactions for a specific user with pagination
     */
    Page<ImageTransaction> findByUserIdOrderByCreatedAtDesc(Long userId, Pageable pageable);

    /**
     * Find all successful image transactions for a specific user with pagination
     */
    Page<ImageTransaction> findByUserIdAndStatusOrderByCreatedAtDesc(
        Long userId, 
        ImageTransaction.TransactionStatus status, 
        Pageable pageable
    );

    /**
     * Find image transaction by Cloudinary public ID
     */
    Optional<ImageTransaction> findByCloudinaryPublicId(String cloudinaryPublicId);

    /**
     * Count total successful uploads for a user
     */
    long countByUserIdAndStatus(Long userId, ImageTransaction.TransactionStatus status);

    /**
     * Find failed transactions within a time range for retry processing
     */
    @Query("{'status': ?0, 'createdAt': {'$gte': ?1, '$lte': ?2}}")
    List<ImageTransaction> findFailedTransactionsInTimeRange(
        ImageTransaction.TransactionStatus status,
        LocalDateTime startTime,
        LocalDateTime endTime
    );

    /**
     * Find transactions by user and status with date range
     */
    @Query("{'userId': ?0, 'status': ?1, 'createdAt': {'$gte': ?2, '$lte': ?3}}")
    List<ImageTransaction> findByUserIdAndStatusAndDateRange(
        Long userId,
        ImageTransaction.TransactionStatus status,
        LocalDateTime startDate,
        LocalDateTime endDate
    );

    /**
     * Find recent transactions for monitoring
     */
    @Query("{'createdAt': {'$gte': ?0}}")
    List<ImageTransaction> findRecentTransactions(LocalDateTime since);

    /**
     * Delete old failed transactions (cleanup job)
     */
    @Query(value = "{'status': ?0, 'createdAt': {'$lt': ?1}}", delete = true)
    void deleteOldFailedTransactions(
        ImageTransaction.TransactionStatus status,
        LocalDateTime cutoffDate
    );

    /**
     * Find transactions that need retry (failed transactions within retry window)
     */
    @Query("{'status': 'FAILED', 'createdAt': {'$gte': ?0}, '$or': [{'errorMessage': null}, {'errorMessage': {'$not': {'$regex': 'PERMANENT'}}}]}")
    List<ImageTransaction> findTransactionsForRetry(LocalDateTime retryWindow);
}
