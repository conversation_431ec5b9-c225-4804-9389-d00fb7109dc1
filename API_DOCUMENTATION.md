# Spark Image Repository API Documentation

## Overview
This API provides secure image upload functionality with Cloudinary integration for a Matrimony application. It supports automatic compression, multiple image transformations, and comprehensive logging.

## Base URL
```
http://localhost:8080/api/v1
```

## Authentication
Currently, the API uses user ID for identification. In production, implement proper JWT/OAuth authentication.

## Endpoints

### 1. Upload Image

**Endpoint:** `POST /images/upload`

**Content-Type:** `multipart/form-data`

**Parameters:**
- `userId` (required): Long - User ID
- `image` (required): File - Image file (JPG, PNG, WEBP)
- `description` (optional): String - Image description
- `category` (optional): String - Image category (e.g., "profile", "gallery")

**Sample Request (cURL):**
```bash
curl -X POST http://localhost:8080/api/v1/images/upload \
  -F "userId=12345" \
  -F "image=@/path/to/image.jpg" \
  -F "description=Profile picture" \
  -F "category=profile"
```

**Sample Success Response:**
```json
{
  "transaction_id": 1,
  "cloudinary_public_id": "user_12345_profile_1699123456789_a1b2c3d4",
  "urls": {
    "thumbnail": "https://res.cloudinary.com/your-cloud/image/upload/w_150,h_150,c_fill,g_face,q_auto,f_auto/user_12345_profile_1699123456789_a1b2c3d4",
    "slider": "https://res.cloudinary.com/your-cloud/image/upload/w_1024,h_768,c_limit,q_auto,f_auto/user_12345_profile_1699123456789_a1b2c3d4",
    "original": "https://res.cloudinary.com/your-cloud/image/upload/q_auto,f_auto/user_12345_profile_1699123456789_a1b2c3d4"
  },
  "status": "SUCCESS",
  "message": "Image uploaded successfully",
  "file_size_info": {
    "original_size_bytes": 2048576,
    "processed_size_bytes": 1536432,
    "compression_ratio": "25.0%"
  },
  "upload_duration_ms": 1250,
  "timestamp": "2023-11-04T15:30:45.123"
}
```

**Sample Error Response:**
```json
{
  "status": "FAILED",
  "message": "Image upload failed",
  "error_message": "File size 12582912 bytes exceeds maximum allowed size 10 MB",
  "timestamp": "2023-11-04T15:30:45.123"
}
```

### 2. Get User Images (Paginated)

**Endpoint:** `GET /images/user/{userId}`

**Parameters:**
- `userId` (path): Long - User ID
- `page` (query, optional): Integer - Page number (default: 0)
- `size` (query, optional): Integer - Page size (default: 20)

**Sample Request:**
```bash
curl -X GET "http://localhost:8080/api/v1/images/user/12345?page=0&size=10"
```

**Sample Response:**
```json
{
  "user_id": 12345,
  "images": [
    {
      "transaction_id": 1,
      "cloudinary_public_id": "user_12345_profile_1699123456789_a1b2c3d4",
      "original_file_name": "profile.jpg",
      "urls": {
        "thumbnail": "https://res.cloudinary.com/your-cloud/image/upload/w_150,h_150,c_fill,g_face,q_auto,f_auto/user_12345_profile_1699123456789_a1b2c3d4",
        "slider": "https://res.cloudinary.com/your-cloud/image/upload/w_1024,h_768,c_limit,q_auto,f_auto/user_12345_profile_1699123456789_a1b2c3d4",
        "original": "https://res.cloudinary.com/your-cloud/image/upload/q_auto,f_auto/user_12345_profile_1699123456789_a1b2c3d4"
      },
      "status": "SUCCESS",
      "uploaded_at": "2023-11-04T15:30:45.123",
      "file_size_bytes": 1536432
    }
  ],
  "total_images": 1,
  "status": "SUCCESS",
  "message": "Images retrieved successfully"
}
```

### 3. Get All User Images

**Endpoint:** `GET /images/user/{userId}/all`

**Parameters:**
- `userId` (path): Long - User ID

**Sample Request:**
```bash
curl -X GET "http://localhost:8080/api/v1/images/user/12345/all"
```

**Response:** Same format as paginated endpoint but returns all images.

### 4. Health Check

**Endpoint:** `GET /images/health`

**Sample Request:**
```bash
curl -X GET "http://localhost:8080/api/v1/images/health"
```

**Response:**
```
Image Upload Service is running
```

## Image Transformations

The API automatically generates three versions of each uploaded image:

1. **Thumbnail (150x150)**: Cropped and optimized for profile pictures with face detection
2. **Slider (1024x768)**: Responsive size for image galleries and sliders
3. **Original**: Full-size image with automatic optimization

## File Validation

- **Supported formats**: JPG, JPEG, PNG, WEBP
- **Maximum file size**: 10 MB
- **Automatic compression**: Files larger than 10 MB are automatically compressed
- **Content type validation**: Ensures uploaded files are valid images

## Error Codes

- `400 Bad Request`: Invalid input, validation errors, or unsupported file format
- `500 Internal Server Error`: Server-side processing errors or Cloudinary issues

## Database Logging

All upload attempts are logged in the `image_transactions` table with:
- User ID and file details
- Cloudinary public ID and URLs
- File sizes (before/after compression)
- Upload status and duration
- Error messages (if any)
- Timestamps

## Frontend Integration

### React Example

```javascript
const uploadImage = async (userId, imageFile, description, category) => {
  const formData = new FormData();
  formData.append('userId', userId);
  formData.append('image', imageFile);
  formData.append('description', description);
  formData.append('category', category);

  try {
    const response = await fetch('/api/v1/images/upload', {
      method: 'POST',
      body: formData,
    });

    const result = await response.json();
    
    if (result.status === 'SUCCESS') {
      console.log('Upload successful:', result);
      return result;
    } else {
      throw new Error(result.error_message || result.message);
    }
  } catch (error) {
    console.error('Upload failed:', error);
    throw error;
  }
};

const getUserImages = async (userId, page = 0, size = 20) => {
  try {
    const response = await fetch(`/api/v1/images/user/${userId}?page=${page}&size=${size}`);
    const result = await response.json();
    
    if (result.status === 'SUCCESS') {
      return result.images;
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('Failed to fetch images:', error);
    throw error;
  }
};
```

## Configuration

### Environment Variables

Set these environment variables for production:

```bash
# Database
DB_USERNAME=your_db_username
DB_PASSWORD=your_db_password

# Cloudinary
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret

# CORS
CORS_ALLOWED_ORIGINS=https://yourdomain.com,https://app.yourdomain.com
```

### Application Properties

Key configuration options in `application.yml`:

```yaml
image:
  max-size-mb: 10
  allowed-formats: jpg,jpeg,png,webp
  compression:
    quality: 85
    max-width: 2048
    max-height: 2048
```
