# MongoDB Setup Guide for Spark Image Repository

This guide provides detailed instructions for setting up MongoDB for the Spark Image Repository API in different environments.

## Table of Contents

1. [Local Development Setup](#local-development-setup)
2. [MongoDB Atlas (Production) Setup](#mongodb-atlas-production-setup)
3. [Environment Configuration](#environment-configuration)
4. [Database Initialization](#database-initialization)
5. [Troubleshooting](#troubleshooting)

## Local Development Setup

### Windows

1. **Download MongoDB Community Server**
   - Visit [MongoDB Download Center](https://www.mongodb.com/try/download/community)
   - Download MongoDB Community Server for Windows
   - Choose the MSI installer

2. **Install MongoDB**
   ```bash
   # Run the downloaded MSI installer
   # Choose "Complete" installation
   # Install MongoDB as a Service (recommended)
   ```

3. **Start MongoDB Service**
   ```bash
   # Start the service
   net start MongoDB
   
   # Verify it's running
   mongo --eval "db.adminCommand('ismaster')"
   ```

4. **Create Database and User (Optional)**
   ```javascript
   // Connect to MongoDB shell
   mongo
   
   // Switch to matrimony database
   use matrimony_dev_db
   
   // Create a user (optional for local development)
   db.createUser({
     user: "matrimony_user",
     pwd: "matrimony_pass",
     roles: [
       { role: "readWrite", db: "matrimony_dev_db" }
     ]
   })
   ```

### macOS

1. **Install using Homebrew**
   ```bash
   # Install MongoDB
   brew tap mongodb/brew
   brew install mongodb-community
   
   # Start MongoDB service
   brew services start mongodb-community
   
   # Verify installation
   mongo --eval "db.adminCommand('ismaster')"
   ```

2. **Manual Installation**
   ```bash
   # Download from MongoDB website
   curl -O https://fastdl.mongodb.org/osx/mongodb-macos-x86_64-6.0.0.tgz
   tar -zxvf mongodb-macos-x86_64-6.0.0.tgz
   
   # Create data directory
   sudo mkdir -p /usr/local/var/mongodb
   sudo mkdir -p /usr/local/var/log/mongodb
   
   # Start MongoDB
   mongod --dbpath /usr/local/var/mongodb --logpath /usr/local/var/log/mongodb/mongo.log --fork
   ```

### Linux (Ubuntu/Debian)

1. **Install MongoDB**
   ```bash
   # Import MongoDB public GPG key
   wget -qO - https://www.mongodb.org/static/pgp/server-6.0.asc | sudo apt-key add -
   
   # Create list file for MongoDB
   echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/6.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-6.0.list
   
   # Update package database
   sudo apt-get update
   
   # Install MongoDB
   sudo apt-get install -y mongodb-org
   
   # Start MongoDB
   sudo systemctl start mongod
   sudo systemctl enable mongod
   
   # Verify installation
   mongo --eval "db.adminCommand('ismaster')"
   ```

## MongoDB Atlas (Production) Setup

### 1. Create MongoDB Atlas Account

1. Visit [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Sign up for a free account
3. Verify your email address

### 2. Create a Cluster

1. **Choose Deployment Option**
   - Select "Shared" for free tier
   - Choose your preferred cloud provider (AWS, Google Cloud, Azure)
   - Select a region close to your users

2. **Configure Cluster**
   - Cluster Name: `matrimony-cluster`
   - MongoDB Version: 6.0 (latest stable)
   - Click "Create Cluster"

### 3. Configure Database Access

1. **Create Database User**
   ```
   Username: matrimony_user
   Password: [Generate secure password]
   Database User Privileges: Read and write to any database
   ```

2. **Note down credentials** for later use

### 4. Configure Network Access

1. **Add IP Addresses**
   - For development: Add your current IP
   - For production: Add your server's IP addresses
   - For testing: You can temporarily use `0.0.0.0/0` (not recommended for production)

### 5. Get Connection String

1. Click "Connect" on your cluster
2. Choose "Connect your application"
3. Select "Java" and version "4.3 or later"
4. Copy the connection string:
   ```
   mongodb+srv://matrimony_user:<password>@matrimony-cluster.xxxxx.mongodb.net/?retryWrites=true&w=majority
   ```

## Environment Configuration

### Development Environment

Create or update `src/main/resources/application-dev.yml`:

```yaml
spring:
  data:
    mongodb:
      uri: mongodb://localhost:27017/matrimony_dev_db
      database: matrimony_dev_db

logging:
  level:
    org.springframework.data.mongodb: DEBUG
    org.mongodb.driver: DEBUG
```

### Production Environment

Create or update `src/main/resources/application-prod.yml`:

```yaml
spring:
  data:
    mongodb:
      uri: ${MONGODB_ATLAS_URI}
      database: ${MONGODB_DATABASE:matrimony_prod_db}

logging:
  level:
    org.springframework.data.mongodb: WARN
    org.mongodb.driver: WARN
```

### Environment Variables

Set these environment variables:

**Development:**
```bash
export SPRING_PROFILES_ACTIVE=dev
```

**Production:**
```bash
export MONGODB_ATLAS_URI="mongodb+srv://matrimony_user:<EMAIL>/matrimony_prod_db?retryWrites=true&w=majority"
export MONGODB_DATABASE=matrimony_prod_db
export SPRING_PROFILES_ACTIVE=prod
```

## Database Initialization

The application automatically:

1. **Creates Collections**: MongoDB creates collections automatically when first document is inserted
2. **Creates Indexes**: The `MongoConfig` class creates necessary indexes on startup
3. **Handles Schema Evolution**: MongoDB's flexible schema allows for easy updates

### Manual Database Setup (Optional)

If you want to pre-create the database and collection:

```javascript
// Connect to MongoDB
mongo "mongodb://localhost:27017"

// Create database
use matrimony_dev_db

// Create collection with validation (optional)
db.createCollection("image_transactions", {
  validator: {
    $jsonSchema: {
      bsonType: "object",
      required: ["userId", "originalFileName", "status"],
      properties: {
        userId: { bsonType: "long" },
        originalFileName: { bsonType: "string" },
        status: { enum: ["PENDING", "PROCESSING", "SUCCESS", "FAILED"] }
      }
    }
  }
})

// Create indexes manually (optional - app creates them automatically)
db.image_transactions.createIndex({ "userId": 1 })
db.image_transactions.createIndex({ "status": 1 })
db.image_transactions.createIndex({ "createdAt": -1 })
db.image_transactions.createIndex({ "cloudinaryPublicId": 1 }, { unique: true })
```

## Troubleshooting

### Common Issues

1. **Connection Refused (Local)**
   ```bash
   # Check if MongoDB is running
   sudo systemctl status mongod  # Linux
   brew services list | grep mongodb  # macOS
   net start MongoDB  # Windows
   ```

2. **Authentication Failed (Atlas)**
   - Verify username and password
   - Check if IP address is whitelisted
   - Ensure connection string is correct

3. **Database Not Found**
   - MongoDB creates databases automatically
   - Ensure connection string includes database name
   - Check if user has proper permissions

4. **Slow Queries**
   - Check if indexes are created properly
   - Monitor query performance in Atlas
   - Use MongoDB Compass for query analysis

### Useful Commands

```bash
# Check MongoDB version
mongo --version

# Connect to local MongoDB
mongo mongodb://localhost:27017

# Connect to Atlas
mongo "mongodb+srv://cluster.xxxxx.mongodb.net" --username matrimony_user

# Show databases
show dbs

# Use specific database
use matrimony_dev_db

# Show collections
show collections

# Check collection stats
db.image_transactions.stats()

# View indexes
db.image_transactions.getIndexes()
```

### Performance Monitoring

**MongoDB Atlas:**
- Use built-in Performance Advisor
- Monitor slow queries in Real-time Performance Panel
- Set up alerts for high CPU/memory usage

**Local MongoDB:**
- Use MongoDB Compass for visual monitoring
- Enable profiling: `db.setProfilingLevel(2)`
- Check slow queries: `db.system.profile.find()`

## Security Best Practices

1. **Never use default passwords**
2. **Limit IP access** to specific addresses
3. **Use strong passwords** for database users
4. **Enable authentication** in production
5. **Use SSL/TLS** for connections
6. **Regular backups** (Atlas provides automatic backups)
7. **Monitor access logs** regularly

## Backup and Recovery

**MongoDB Atlas:**
- Automatic backups enabled by default
- Point-in-time recovery available
- Download backups via Atlas UI

**Local MongoDB:**
```bash
# Create backup
mongodump --db matrimony_dev_db --out /path/to/backup

# Restore backup
mongorestore --db matrimony_dev_db /path/to/backup/matrimony_dev_db
```
