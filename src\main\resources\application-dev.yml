# Development Environment Configuration
spring:
  data:
    mongodb:
      uri: mongodb://localhost:27017/matrimony_dev_db
      database: matrimony_dev_db

# Logging for development
logging:
  level:
    com.matrimony.sparkimagerepo: DEBUG
    org.springframework.data.mongodb: DEBUG
    org.mongodb.driver: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Development Cloudinary (use test credentials)
cloudinary:
  cloud-name: ${CLOUDINARY_CLOUD_NAME:dev-cloud-name}
  api-key: ${CLOUDINARY_API_KEY:dev-api-key}
  api-secret: ${CLOUDINARY_API_SECRET:dev-api-secret}
  secure: true
