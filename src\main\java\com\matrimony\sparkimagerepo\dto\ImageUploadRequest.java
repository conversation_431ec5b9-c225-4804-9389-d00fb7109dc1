package com.matrimony.sparkimagerepo.dto;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import org.springframework.web.multipart.MultipartFile;

public class ImageUploadRequest {

    @NotNull(message = "User ID is required")
    @Positive(message = "User ID must be positive")
    private Long userId;

    @NotNull(message = "Image file is required")
    private MultipartFile image;

    private String description;

    private String category; // e.g., "profile", "gallery", "verification"

    // Constructors
    public ImageUploadRequest() {}

    public ImageUploadRequest(Long userId, MultipartFile image) {
        this.userId = userId;
        this.image = image;
    }

    public ImageUploadRequest(Long userId, MultipartFile image, String description, String category) {
        this.userId = userId;
        this.image = image;
        this.description = description;
        this.category = category;
    }

    // Getters and Setters
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public MultipartFile getImage() {
        return image;
    }

    public void setImage(MultipartFile image) {
        this.image = image;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }
}
