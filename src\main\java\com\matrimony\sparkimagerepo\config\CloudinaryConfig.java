package com.matrimony.sparkimagerepo.config;

import com.cloudinary.Cloudinary;
import com.cloudinary.utils.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CloudinaryConfig {

    private static final Logger logger = LoggerFactory.getLogger(CloudinaryConfig.class);

    @Value("${cloudinary.cloud-name}")
    private String cloudName;

    @Value("${cloudinary.api-key}")
    private String apiKey;

    @Value("${cloudinary.api-secret}")
    private String apiSecret;

    @Value("${cloudinary.secure:true}")
    private boolean secure;

    @Bean
    public Cloudinary cloudinary() {
        logger.info("Initializing Cloudinary with cloud name: {}", cloudName);
        
        Cloudinary cloudinary = new Cloudinary(ObjectUtils.asMap(
            "cloud_name", cloudName,
            "api_key", apiKey,
            "api_secret", apiSecret,
            "secure", secure
        ));

        // Test the connection
        try {
            cloudinary.api().ping(ObjectUtils.emptyMap());
            logger.info("Cloudinary connection successful");
        } catch (Exception e) {
            logger.error("Failed to connect to Cloudinary", e);
            throw new RuntimeException("Cloudinary configuration failed", e);
        }

        return cloudinary;
    }
}
