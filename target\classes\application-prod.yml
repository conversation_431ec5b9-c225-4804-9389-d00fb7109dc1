# Production Environment Configuration
spring:
  data:
    mongodb:
      uri: ${MONGODB_ATLAS_URI:mongodb+srv://sparkdevuser:<EMAIL>/?retryWrites=true&w=majority&appName=sparkmate1}
      database: ${MONGODB_DATABASE:spark_image_repo}

# Production logging (less verbose)
logging:
  level:
    com.matrimony.sparkimagerepo: INFO
    org.springframework.data.mongodb: WARN
    org.mongodb.driver: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/spark-image-repo-prod.log

# Production Cloudinary
cloudinary:
  cloud-name: ${CLOUDINARY_CLOUD_NAME}
  api-key: ${CLOUDINARY_API_KEY}
  api-secret: ${CLOUDINARY_API_SECRET}
  secure: true

# Production CORS settings
app:
  cors:
    allowed-origins: ${CORS_ALLOWED_ORIGINS:https://yourdomain.com}
    allowed-methods: GET,POST,PUT,DELETE,OPTIONS
    allowed-headers: "*"
    allow-credentials: true
