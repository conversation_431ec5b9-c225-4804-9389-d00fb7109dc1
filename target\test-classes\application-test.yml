spring:
  data:
    mongodb:
      uri: mongodb://localhost:27017/matrimony_test_db
      database: matrimony_test_db

# Test Cloudinary Configuration (mock values)
cloudinary:
  cloud-name: test-cloud
  api-key: test-key
  api-secret: test-secret
  secure: true

# Test Image Configuration
image:
  max-size-mb: 5
  allowed-formats: jpg,jpeg,png
  compression:
    quality: 80
    max-width: 1024
    max-height: 1024

# Logging for tests
logging:
  level:
    com.matrimony.sparkimagerepo: DEBUG
    org.springframework.web: DEBUG
